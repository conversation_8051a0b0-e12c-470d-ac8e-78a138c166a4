import { supabase } from './supabase'
import { ExtendedDatabase } from './database-extended'

export interface StorageUsageData {
  overview: {
    totalDatabaseSize: string
    totalDatabaseSizeBytes: number
    walSize: string
    walSizeBytes: number
    systemSize: string
    systemSizeBytes: number
    totalDiskUsage: string
    totalDiskUsageBytes: number
    lastUpdated: string
  }
  tables: {
    tableName: string
    schemaName: string
    size: string
    sizeBytes: number
    rowCount: number
    percentage: number
  }[]
  capacity: {
    freeLimit: number // 500MB for free plan
    proLimit: number // 8GB for pro plan
    currentUsagePercentage: number
    remainingSpace: string
    remainingSpaceBytes: number
    planType: 'free' | 'pro' | 'unknown'
  }
  projections: {
    toolCount1w: {
      estimatedSize: string
      estimatedSizeBytes: number
      willExceedFree: boolean
      willExceedPro: boolean
    }
    toolCount5w: {
      estimatedSize: string
      estimatedSizeBytes: number
      willExceedFree: boolean
      willExceedPro: boolean
    }
    toolCount10w: {
      estimatedSize: string
      estimatedSizeBytes: number
      willExceedFree: boolean
      willExceedPro: boolean
    }
  }
}

export class StorageMonitoringService {
  
  // 获取数据库总大小
  private static async getDatabaseSize(): Promise<{ size: string; sizeBytes: number }> {
    try {
      const { data, error } = await supabase.rpc('get_database_size')
      
      if (error) {
        // 如果 RPC 函数不存在，使用原生 SQL 查询
        const { data: rawData, error: sqlError } = await supabase
          .from('pg_database')
          .select('*')
        
        if (sqlError) {
          console.warn('无法获取数据库大小，使用估算值:', sqlError)
          return { size: '未知', sizeBytes: 0 }
        }
      }
      
      return data || { size: '未知', sizeBytes: 0 }
    } catch (error) {
      console.error('获取数据库大小失败:', error)
      return { size: '未知', sizeBytes: 0 }
    }
  }

  // 获取 WAL 文件大小
  private static async getWalSize(): Promise<{ size: string; sizeBytes: number }> {
    try {
      const { data, error } = await supabase.rpc('get_wal_size')
      
      if (error) {
        console.warn('无法获取 WAL 大小:', error)
        return { size: '未知', sizeBytes: 0 }
      }
      
      return data || { size: '未知', sizeBytes: 0 }
    } catch (error) {
      console.error('获取 WAL 大小失败:', error)
      return { size: '未知', sizeBytes: 0 }
    }
  }

  // 获取表级别存储使用情况
  private static async getTableSizes(): Promise<StorageUsageData['tables']> {
    try {
      const { data, error } = await supabase.rpc('get_table_sizes')
      
      if (error) {
        console.warn('无法获取表大小，使用估算:', error)
        return await this.estimateTableSizes()
      }
      
      return data || []
    } catch (error) {
      console.error('获取表大小失败:', error)
      return await this.estimateTableSizes()
    }
  }

  // 估算表大小（当无法直接查询时）
  private static async estimateTableSizes(): Promise<StorageUsageData['tables']> {
    try {
      // 获取工具数量
      const { data: tools, error: toolsError } = await supabase
        .from('tools')
        .select('id', { count: 'exact' })
      
      const toolCount = tools?.length || 0
      
      // 获取其他表的记录数
      const tables = [
        { name: 'tools', schema: 'public' },
        { name: 'categories', schema: 'public' },
        { name: 'operation_logs', schema: 'public' },
        { name: 'import_tasks', schema: 'public' },
        { name: 'data_statistics', schema: 'public' }
      ]
      
      const tableStats = []
      let totalEstimatedBytes = 0
      
      for (const table of tables) {
        let estimatedBytes = 0
        let rowCount = 0
        
        try {
          const { count } = await supabase
            .from(table.name)
            .select('*', { count: 'exact', head: true })
          
          rowCount = count || 0
          
          // 根据表类型估算每行大小
          switch (table.name) {
            case 'tools':
              estimatedBytes = rowCount * 1024 // 每个工具约 1KB
              break
            case 'categories':
              estimatedBytes = rowCount * 10240 // 每个分类约 10KB (JSONB)
              break
            case 'operation_logs':
              estimatedBytes = rowCount * 512 // 每条日志约 512B
              break
            case 'import_tasks':
              estimatedBytes = rowCount * 2048 // 每个任务约 2KB
              break
            case 'data_statistics':
              estimatedBytes = rowCount * 1024 // 每条统计约 1KB
              break
            default:
              estimatedBytes = rowCount * 256 // 默认每行 256B
          }
          
          totalEstimatedBytes += estimatedBytes
          
          tableStats.push({
            tableName: table.name,
            schemaName: table.schema,
            size: this.formatBytes(estimatedBytes),
            sizeBytes: estimatedBytes,
            rowCount,
            percentage: 0 // 稍后计算
          })
        } catch (error) {
          console.warn(`估算表 ${table.name} 大小失败:`, error)
        }
      }
      
      // 计算百分比
      tableStats.forEach(table => {
        table.percentage = totalEstimatedBytes > 0 
          ? Math.round((table.sizeBytes / totalEstimatedBytes) * 100)
          : 0
      })
      
      return tableStats.sort((a, b) => b.sizeBytes - a.sizeBytes)
    } catch (error) {
      console.error('估算表大小失败:', error)
      return []
    }
  }

  // 格式化字节数
  private static formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 计算容量预估
  private static calculateProjections(
    currentToolCount: number,
    averageSizePerTool: number
  ): StorageUsageData['projections'] {
    const freeLimit = 500 * 1024 * 1024 // 500MB
    const proLimit = 8 * 1024 * 1024 * 1024 // 8GB
    
    const calculateForToolCount = (toolCount: number) => {
      const estimatedSizeBytes = toolCount * averageSizePerTool
      return {
        estimatedSize: this.formatBytes(estimatedSizeBytes),
        estimatedSizeBytes,
        willExceedFree: estimatedSizeBytes > freeLimit,
        willExceedPro: estimatedSizeBytes > proLimit
      }
    }
    
    return {
      toolCount1w: calculateForToolCount(10000),
      toolCount5w: calculateForToolCount(50000),
      toolCount10w: calculateForToolCount(100000)
    }
  }

  // 获取完整的存储使用情况
  static async getStorageUsage(): Promise<StorageUsageData> {
    try {
      // 检查缓存
      const cached = await ExtendedDatabase.getDataStatistics('storage', 'usage')
      if (cached && cached.expires_at && new Date(cached.expires_at) > new Date()) {
        return cached.stat_value as StorageUsageData
      }

      // 获取基础数据
      const [databaseSize, walSize, tableSizes] = await Promise.all([
        this.getDatabaseSize(),
        this.getWalSize(),
        this.getTableSizes()
      ])

      // 获取工具数量用于预估
      const { data: tools } = await supabase
        .from('tools')
        .select('id', { count: 'exact' })
      
      const currentToolCount = tools?.length || 0
      const totalTableBytes = tableSizes.reduce((sum, table) => sum + table.sizeBytes, 0)
      const averageSizePerTool = currentToolCount > 0 ? totalTableBytes / currentToolCount : 1024

      // 计算总磁盘使用量
      const totalDiskUsageBytes = databaseSize.sizeBytes + walSize.sizeBytes
      const systemSizeBytes = Math.max(0, totalDiskUsageBytes - databaseSize.sizeBytes - walSize.sizeBytes)

      // 容量限制和使用情况
      const freeLimit = 500 * 1024 * 1024 // 500MB
      const proLimit = 8 * 1024 * 1024 * 1024 // 8GB
      const currentUsagePercentage = Math.round((totalDiskUsageBytes / freeLimit) * 100)
      const remainingSpaceBytes = Math.max(0, freeLimit - totalDiskUsageBytes)

      const storageData: StorageUsageData = {
        overview: {
          totalDatabaseSize: databaseSize.size,
          totalDatabaseSizeBytes: databaseSize.sizeBytes,
          walSize: walSize.size,
          walSizeBytes: walSize.sizeBytes,
          systemSize: this.formatBytes(systemSizeBytes),
          systemSizeBytes,
          totalDiskUsage: this.formatBytes(totalDiskUsageBytes),
          totalDiskUsageBytes,
          lastUpdated: new Date().toISOString()
        },
        tables: tableSizes,
        capacity: {
          freeLimit,
          proLimit,
          currentUsagePercentage,
          remainingSpace: this.formatBytes(remainingSpaceBytes),
          remainingSpaceBytes,
          planType: 'free' // 默认假设为免费计划
        },
        projections: this.calculateProjections(currentToolCount, averageSizePerTool)
      }

      // 缓存结果（5分钟过期）
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString()
      await ExtendedDatabase.setDataStatistics('storage', 'usage', storageData, expiresAt)

      return storageData
    } catch (error) {
      console.error('获取存储使用情况失败:', error)
      
      // 返回默认数据
      return {
        overview: {
          totalDatabaseSize: '未知',
          totalDatabaseSizeBytes: 0,
          walSize: '未知',
          walSizeBytes: 0,
          systemSize: '未知',
          systemSizeBytes: 0,
          totalDiskUsage: '未知',
          totalDiskUsageBytes: 0,
          lastUpdated: new Date().toISOString()
        },
        tables: [],
        capacity: {
          freeLimit: 500 * 1024 * 1024,
          proLimit: 8 * 1024 * 1024 * 1024,
          currentUsagePercentage: 0,
          remainingSpace: '未知',
          remainingSpaceBytes: 0,
          planType: 'unknown'
        },
        projections: {
          toolCount1w: {
            estimatedSize: '未知',
            estimatedSizeBytes: 0,
            willExceedFree: false,
            willExceedPro: false
          },
          toolCount5w: {
            estimatedSize: '未知',
            estimatedSizeBytes: 0,
            willExceedFree: false,
            willExceedPro: false
          },
          toolCount10w: {
            estimatedSize: '未知',
            estimatedSizeBytes: 0,
            willExceedFree: false,
            willExceedPro: false
          }
        }
      }
    }
  }

  // 清除缓存
  static async clearCache(): Promise<void> {
    try {
      await supabase
        .from('data_statistics')
        .delete()
        .eq('stat_type', 'storage')
        .eq('stat_key', 'usage')
    } catch (error) {
      console.error('清除存储监控缓存失败:', error)
    }
  }
}
