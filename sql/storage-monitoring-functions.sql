-- ToolMaster 存储监控 SQL 函数
-- 在 Supabase SQL Editor 中执行此脚本

-- ===========================================
-- 1. 获取数据库总大小的函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_database_size()
RETURNS TABLE(size text, size_bytes bigint)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pg_size_pretty(sum(pg_database_size(pg_database.datname))) as size,
    sum(pg_database_size(pg_database.datname)) as size_bytes
  FROM pg_database;
END;
$$;

-- ===========================================
-- 2. 获取 WAL 文件大小的函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_wal_size()
RETURNS TABLE(size text, size_bytes bigint)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    pg_size_pretty(sum(size)) as size,
    sum(size) as size_bytes
  FROM pg_ls_waldir();
EXCEPTION
  WHEN OTHERS THEN
    -- 如果无法访问 WAL 目录，返回默认值
    RETURN QUERY
    SELECT '未知'::text as size, 0::bigint as size_bytes;
END;
$$;

-- ===========================================
-- 3. 获取表级别存储大小的函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_table_sizes()
RETURNS TABLE(
  table_name text,
  schema_name text,
  size text,
  size_bytes bigint,
  row_count bigint,
  percentage numeric
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  total_size bigint := 0;
  rec record;
BEGIN
  -- 创建临时表存储结果
  CREATE TEMP TABLE temp_table_sizes (
    table_name text,
    schema_name text,
    size text,
    size_bytes bigint,
    row_count bigint,
    percentage numeric
  );

  -- 获取所有公共表的大小
  FOR rec IN 
    SELECT 
      t.tablename,
      t.schemaname,
      pg_total_relation_size(t.schemaname||'.'||t.tablename) as bytes,
      pg_size_pretty(pg_total_relation_size(t.schemaname||'.'||t.tablename)) as pretty_size
    FROM pg_tables t 
    WHERE t.schemaname = 'public'
    ORDER BY pg_total_relation_size(t.schemaname||'.'||t.tablename) DESC
  LOOP
    -- 获取行数
    DECLARE
      row_count_val bigint;
      sql_text text;
    BEGIN
      sql_text := 'SELECT COUNT(*) FROM ' || quote_ident(rec.schemaname) || '.' || quote_ident(rec.tablename);
      EXECUTE sql_text INTO row_count_val;
      
      -- 插入临时表
      INSERT INTO temp_table_sizes VALUES (
        rec.tablename,
        rec.schemaname,
        rec.pretty_size,
        rec.bytes,
        row_count_val,
        0 -- 百分比稍后计算
      );
      
      total_size := total_size + rec.bytes;
    EXCEPTION
      WHEN OTHERS THEN
        -- 如果无法获取行数，使用 0
        INSERT INTO temp_table_sizes VALUES (
          rec.tablename,
          rec.schemaname,
          rec.pretty_size,
          rec.bytes,
          0,
          0
        );
        total_size := total_size + rec.bytes;
    END;
  END LOOP;

  -- 更新百分比
  IF total_size > 0 THEN
    UPDATE temp_table_sizes 
    SET percentage = ROUND((size_bytes::numeric / total_size::numeric) * 100, 2);
  END IF;

  -- 返回结果
  RETURN QUERY
  SELECT * FROM temp_table_sizes
  ORDER BY size_bytes DESC;

  -- 清理临时表
  DROP TABLE temp_table_sizes;
END;
$$;

-- ===========================================
-- 4. 获取数据库连接和活动信息的函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_database_activity()
RETURNS TABLE(
  active_connections integer,
  max_connections integer,
  connection_usage_percentage numeric,
  longest_query_duration interval,
  active_queries integer
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active')::integer as active_connections,
    (SELECT setting::integer FROM pg_settings WHERE name = 'max_connections') as max_connections,
    ROUND(
      (SELECT count(*) FROM pg_stat_activity WHERE state = 'active')::numeric / 
      (SELECT setting::numeric FROM pg_settings WHERE name = 'max_connections') * 100, 
      2
    ) as connection_usage_percentage,
    (SELECT COALESCE(MAX(now() - query_start), '0 seconds'::interval) FROM pg_stat_activity WHERE state = 'active') as longest_query_duration,
    (SELECT count(*) FROM pg_stat_activity WHERE state = 'active' AND query NOT LIKE '%pg_stat_activity%')::integer as active_queries;
END;
$$;

-- ===========================================
-- 5. 获取索引使用情况的函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_index_usage()
RETURNS TABLE(
  table_name text,
  index_name text,
  index_size text,
  index_size_bytes bigint,
  index_scans bigint,
  tuples_read bigint,
  tuples_fetched bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.relname::text as table_name,
    i.relname::text as index_name,
    pg_size_pretty(pg_relation_size(i.oid)) as index_size,
    pg_relation_size(i.oid) as index_size_bytes,
    COALESCE(s.idx_scan, 0) as index_scans,
    COALESCE(s.idx_tup_read, 0) as tuples_read,
    COALESCE(s.idx_tup_fetch, 0) as tuples_fetched
  FROM pg_class i
  JOIN pg_index ix ON i.oid = ix.indexrelid
  JOIN pg_class t ON ix.indrelid = t.oid
  LEFT JOIN pg_stat_user_indexes s ON i.oid = s.indexrelid
  WHERE t.relkind = 'r'
    AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  ORDER BY pg_relation_size(i.oid) DESC;
END;
$$;

-- ===========================================
-- 6. 获取表统计信息的函数
-- ===========================================
CREATE OR REPLACE FUNCTION get_table_statistics()
RETURNS TABLE(
  table_name text,
  row_count bigint,
  table_size text,
  table_size_bytes bigint,
  index_size text,
  index_size_bytes bigint,
  total_size text,
  total_size_bytes bigint,
  last_vacuum timestamp with time zone,
  last_analyze timestamp with time zone,
  n_tup_ins bigint,
  n_tup_upd bigint,
  n_tup_del bigint
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.relname::text as table_name,
    COALESCE(s.n_tup_ins + s.n_tup_upd + s.n_tup_del - s.n_tup_del, s.reltuples::bigint, 0) as row_count,
    pg_size_pretty(pg_relation_size(t.oid)) as table_size,
    pg_relation_size(t.oid) as table_size_bytes,
    pg_size_pretty(pg_indexes_size(t.oid)) as index_size,
    pg_indexes_size(t.oid) as index_size_bytes,
    pg_size_pretty(pg_total_relation_size(t.oid)) as total_size,
    pg_total_relation_size(t.oid) as total_size_bytes,
    s.last_vacuum,
    s.last_analyze,
    COALESCE(s.n_tup_ins, 0) as n_tup_ins,
    COALESCE(s.n_tup_upd, 0) as n_tup_upd,
    COALESCE(s.n_tup_del, 0) as n_tup_del
  FROM pg_class t
  LEFT JOIN pg_stat_user_tables s ON t.oid = s.relid
  WHERE t.relkind = 'r'
    AND t.relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public')
  ORDER BY pg_total_relation_size(t.oid) DESC;
END;
$$;

-- ===========================================
-- 7. 权限设置
-- ===========================================
-- 授予匿名用户执行这些函数的权限
GRANT EXECUTE ON FUNCTION get_database_size() TO anon;
GRANT EXECUTE ON FUNCTION get_wal_size() TO anon;
GRANT EXECUTE ON FUNCTION get_table_sizes() TO anon;
GRANT EXECUTE ON FUNCTION get_database_activity() TO anon;
GRANT EXECUTE ON FUNCTION get_index_usage() TO anon;
GRANT EXECUTE ON FUNCTION get_table_statistics() TO anon;

-- 授予认证用户执行这些函数的权限
GRANT EXECUTE ON FUNCTION get_database_size() TO authenticated;
GRANT EXECUTE ON FUNCTION get_wal_size() TO authenticated;
GRANT EXECUTE ON FUNCTION get_table_sizes() TO authenticated;
GRANT EXECUTE ON FUNCTION get_database_activity() TO authenticated;
GRANT EXECUTE ON FUNCTION get_index_usage() TO authenticated;
GRANT EXECUTE ON FUNCTION get_table_statistics() TO authenticated;

-- ===========================================
-- 8. 创建视图以便更容易查询
-- ===========================================
CREATE OR REPLACE VIEW storage_overview AS
SELECT 
  'database' as component,
  (SELECT size FROM get_database_size() LIMIT 1) as size,
  (SELECT size_bytes FROM get_database_size() LIMIT 1) as size_bytes
UNION ALL
SELECT 
  'wal' as component,
  (SELECT size FROM get_wal_size() LIMIT 1) as size,
  (SELECT size_bytes FROM get_wal_size() LIMIT 1) as size_bytes;

-- 授予视图访问权限
GRANT SELECT ON storage_overview TO anon;
GRANT SELECT ON storage_overview TO authenticated;

-- ===========================================
-- 9. 注释说明
-- ===========================================
COMMENT ON FUNCTION get_database_size() IS 'ToolMaster: 获取数据库总大小';
COMMENT ON FUNCTION get_wal_size() IS 'ToolMaster: 获取WAL文件大小';
COMMENT ON FUNCTION get_table_sizes() IS 'ToolMaster: 获取各表存储大小详情';
COMMENT ON FUNCTION get_database_activity() IS 'ToolMaster: 获取数据库连接和活动信息';
COMMENT ON FUNCTION get_index_usage() IS 'ToolMaster: 获取索引使用情况';
COMMENT ON FUNCTION get_table_statistics() IS 'ToolMaster: 获取表统计信息';
COMMENT ON VIEW storage_overview IS 'ToolMaster: 存储使用概览视图';
